'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useTranslations } from 'next-intl'
import { ChevronLeft, CheckCircle, Timer, BarChart3, PiggyBank, LineChart, Clock, CreditCard, Shield } from 'lucide-react'
import PublicLayout from '@/components/layout/PublicLayout'
import { Card, CardContent } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'

export default function FeaturesPage() {
    const t = useTranslations()

    // Danh sách các tính năng chính
    const mainFeatures = [
        {
            id: 'expense-tracking',
            title: t('features.mainFeatures.expenseTracking.title'),
            description: t('features.mainFeatures.expenseTracking.description'),
            benefits: t.raw('features.mainFeatures.expenseTracking.benefits') as string[],
            icon: <BarChart3 className="w-8 h-8" />
        },
        {
            id: 'budget-management',
            title: t('features.mainFeatures.budgetManagement.title'),
            description: t('features.mainFeatures.budgetManagement.description'),
            benefits: t.raw('features.mainFeatures.budgetManagement.benefits') as string[],
            icon: <PiggyBank className="w-8 h-8" />
        },
        {
            id: 'financial-analysis',
            title: t('features.mainFeatures.financialAnalysis.title'),
            description: t('features.mainFeatures.financialAnalysis.description'),
            benefits: t.raw('features.mainFeatures.financialAnalysis.benefits') as string[],
            icon: <LineChart className="w-8 h-8" />
        },
        {
            id: 'future-planning',
            title: t('features.mainFeatures.futurePlanning.title'),
            description: t('features.mainFeatures.futurePlanning.description'),
            benefits: t.raw('features.mainFeatures.futurePlanning.benefits') as string[],
            icon: <Clock className="w-8 h-8" />
        },
        {
            id: 'loan-management',
            title: t('features.mainFeatures.loanManagement.title'),
            description: t('features.mainFeatures.loanManagement.description'),
            benefits: t.raw('features.mainFeatures.loanManagement.benefits') as string[],
            icon: <CreditCard className="w-8 h-8" />
        },
        {
            id: 'data-security',
            title: t('features.mainFeatures.dataSecurity.title'),
            description: t('features.mainFeatures.dataSecurity.description'),
            benefits: t.raw('features.mainFeatures.dataSecurity.benefits') as string[],
            icon: <Shield className="w-8 h-8" />
        }
    ];

    // Tính năng sắp ra mắt
    const comingSoonFeatures = [
        {
            title: t('features.comingSoon.bankSync.title'),
            description: t('features.comingSoon.bankSync.description'),
            eta: t('features.comingSoon.bankSync.eta'),
            icon: "🏦"
        },
        {
            title: t('features.comingSoon.groupExpense.title'),
            description: t('features.comingSoon.groupExpense.description'),
            eta: t('features.comingSoon.groupExpense.eta'),
            icon: "👥"
        },
        {
            title: t('features.comingSoon.aiAdvisor.title'),
            description: t('features.comingSoon.aiAdvisor.description'),
            eta: t('features.comingSoon.aiAdvisor.eta'),
            icon: "🤖"
        }
    ];

    return (
        <PublicLayout>
            <div className="container mx-auto py-12 px-4 sm:px-6 lg:px-8">
                <div className="mb-12">
                    <Link href="/" className="flex items-center text-indigo-600 hover:text-indigo-800 mb-4">
                        <ChevronLeft className="w-5 h-5 mr-1" />
                        <span>{t('common.backToHome')}</span>
                    </Link>
                    <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                        {t('features.title')}
                    </h1>
                    <p className="text-xl text-gray-600 dark:text-gray-300">
                        {t('features.subtitle')}
                    </p>
                </div>

                {/* Mô tả tổng quan */}
                <div className="mb-16">
                    <Card>
                        <CardContent className="p-8">
                            <p className="text-lg leading-relaxed">
                                {t('features.description')}
                            </p>
                        </CardContent>
                    </Card>
                </div>

                {/* Tính năng chính */}
                <div className="mb-20">
                    <h2 className="text-3xl font-bold mb-10 text-center">{t('features.mainFeatures.title')}</h2>
                    <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
                        {mainFeatures.map((feature, index) => (
                            <Card key={feature.id} className="overflow-hidden shadow-lg hover:shadow-xl transition-shadow">
                                <CardContent className="p-6">
                                    <div className="flex items-center mb-4">
                                        <div className="bg-indigo-100 p-3 rounded-lg text-indigo-600 mr-4 dark:bg-indigo-900 dark:text-indigo-300">
                                            {feature.icon}
                                        </div>
                                        <h3 className="text-xl font-bold">{feature.title}</h3>
                                    </div>
                                    <p className="text-gray-700 dark:text-gray-300 mb-4">{feature.description}</p>

                                    {/* Benefits */}
                                    {feature.benefits && feature.benefits.length > 0 && (
                                        <div className="space-y-2">
                                            <p className="font-semibold text-gray-900 dark:text-white">{t('features.benefits')}:</p>
                                            <ul className="space-y-2">
                                                {feature.benefits.map((benefit: string, benefitIndex: number) => (
                                                    <li key={benefitIndex} className="flex items-start">
                                                        <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                                                        <span className="text-sm">{benefit}</span>
                                                    </li>
                                                ))}
                                            </ul>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                </div>

                {/* Tính năng sắp ra mắt */}
                <div>
                    <div className="flex items-center mb-8">
                        <Timer className="h-8 w-8 text-indigo-600 mr-3" />
                        <h2 className="text-3xl font-bold">{t('features.comingSoon.title')}</h2>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                        {comingSoonFeatures.map((feature, index) => (
                            <Card key={index} className="border-dashed border-2 border-indigo-200 bg-indigo-50 dark:bg-indigo-900/10">
                                <CardContent className="p-6">
                                    <div className="flex items-center mb-4">
                                        <div className="text-2xl mr-3">
                                            {feature.icon}
                                        </div>
                                        <div className="flex-1">
                                            <div className="flex justify-between items-start">
                                                <h3 className="text-xl font-bold text-indigo-700 dark:text-indigo-400">{feature.title}</h3>
                                                <span className="text-xs bg-indigo-100 text-indigo-800 px-2 py-1 rounded dark:bg-indigo-800 dark:text-indigo-200 ml-2">
                                                    {feature.eta}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <p className="text-gray-700 dark:text-gray-300">{feature.description}</p>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                </div>

                {/* CTA */}
                <div className="mt-20 text-center">
                    <h3 className="text-2xl font-bold mb-4">{t('home.cta.title')}</h3>
                    <p className="text-gray-600 dark:text-gray-300 mb-6">
                        {t('home.cta.description')}
                    </p>
                    <div className="flex flex-col sm:flex-row justify-center gap-4">
                        <Link href="/register">
                            <Button size="lg" className="px-8">
                                {t('home.cta.getStarted')}
                            </Button>
                        </Link>
                        <Link href="/contact">
                            <Button size="lg" variant="outline" className="px-8">
                                {t('home.hero.learnMore')}
                            </Button>
                        </Link>
                    </div>
                </div>
            </div>
        </PublicLayout>
    )
}
