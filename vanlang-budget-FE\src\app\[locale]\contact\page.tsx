'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useTranslations } from 'next-intl'
import { ChevronLeft, Mail, Phone, MapPin, Clock, Send } from 'lucide-react'
import PublicLayout from '@/components/layout/PublicLayout'
import { Card, CardContent } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'

export default function ContactPage() {
    const t = useTranslations()
    const [formData, setFormData] = useState({
        name: '',
        email: '',
        subject: '',
        message: '',
        category: ''
    })

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault()
        // Handle form submission here
        console.log('Form submitted:', formData)
        // You can add actual form submission logic here
    }

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
        setFormData({
            ...formData,
            [e.target.name]: e.target.value
        })
    }

    const supportCategories = [
        {
            title: t('contact.supportCategories.technical.title'),
            description: t('contact.supportCategories.technical.description'),
            email: t('contact.supportCategories.technical.email'),
            icon: <Mail className="h-6 w-6" />
        },
        {
            title: t('contact.supportCategories.feedback.title'),
            description: t('contact.supportCategories.feedback.description'),
            email: t('contact.supportCategories.feedback.email'),
            icon: <Send className="h-6 w-6" />
        },
        {
            title: t('contact.supportCategories.business.title'),
            description: t('contact.supportCategories.business.description'),
            email: t('contact.supportCategories.business.email'),
            icon: <Phone className="h-6 w-6" />
        }
    ]

    const faqs = t.raw('contact.faq.questions') as Array<{question: string, answer: string}>

    return (
        <PublicLayout>
            <div className="container mx-auto py-12 px-4 sm:px-6 lg:px-8">
                <div className="mb-12">
                    <Link href="/" className="flex items-center text-indigo-600 hover:text-indigo-800 mb-4">
                        <ChevronLeft className="w-5 h-5 mr-1" />
                        <span>{t('common.backToHome')}</span>
                    </Link>
                    <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                        {t('contact.title')}
                    </h1>
                    <p className="text-xl text-gray-600 dark:text-gray-300">
                        {t('contact.subtitle')}
                    </p>
                </div>

                {/* Mô tả */}
                <div className="mb-16">
                    <Card>
                        <CardContent className="p-8">
                            <p className="text-lg leading-relaxed">
                                {t('contact.description')}
                            </p>
                        </CardContent>
                    </Card>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                    {/* Contact Information */}
                    <div>
                        <h2 className="text-2xl font-bold mb-8">{t('contact.contactInfo.title')}</h2>
                        
                        <div className="space-y-6">
                            <div className="flex items-start">
                                <Mail className="h-6 w-6 text-indigo-600 mt-1 mr-4" />
                                <div>
                                    <h3 className="font-semibold">{t('contact.contactInfo.emailLabel')}</h3>
                                    <p className="text-gray-600 dark:text-gray-300">{t('contact.contactInfo.email')}</p>
                                </div>
                            </div>
                            
                            <div className="flex items-start">
                                <Phone className="h-6 w-6 text-indigo-600 mt-1 mr-4" />
                                <div>
                                    <h3 className="font-semibold">{t('contact.contactInfo.phoneLabel')}</h3>
                                    <p className="text-gray-600 dark:text-gray-300">{t('contact.contactInfo.phone')}</p>
                                </div>
                            </div>
                            
                            <div className="flex items-start">
                                <MapPin className="h-6 w-6 text-indigo-600 mt-1 mr-4" />
                                <div>
                                    <h3 className="font-semibold">{t('contact.contactInfo.addressLabel')}</h3>
                                    <p className="text-gray-600 dark:text-gray-300">{t('contact.contactInfo.address')}</p>
                                </div>
                            </div>
                            
                            <div className="flex items-start">
                                <Clock className="h-6 w-6 text-indigo-600 mt-1 mr-4" />
                                <div>
                                    <h3 className="font-semibold">{t('contact.contactInfo.workingHoursLabel')}</h3>
                                    <p className="text-gray-600 dark:text-gray-300">{t('contact.contactInfo.workingHours')}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Contact Form */}
                    <div>
                        <h2 className="text-2xl font-bold mb-8">{t('contact.form.title')}</h2>
                        
                        <Card>
                            <CardContent className="p-6">
                                <form onSubmit={handleSubmit} className="space-y-6">
                                    <div>
                                        <label htmlFor="name" className="block text-sm font-medium mb-2">
                                            {t('contact.form.fields.name')}
                                        </label>
                                        <input
                                            type="text"
                                            id="name"
                                            name="name"
                                            value={formData.name}
                                            onChange={handleChange}
                                            placeholder={t('contact.form.placeholders.name')}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600"
                                            required
                                        />
                                    </div>

                                    <div>
                                        <label htmlFor="email" className="block text-sm font-medium mb-2">
                                            {t('contact.form.fields.email')}
                                        </label>
                                        <input
                                            type="email"
                                            id="email"
                                            name="email"
                                            value={formData.email}
                                            onChange={handleChange}
                                            placeholder={t('contact.form.placeholders.email')}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600"
                                            required
                                        />
                                    </div>

                                    <div>
                                        <label htmlFor="subject" className="block text-sm font-medium mb-2">
                                            {t('contact.form.fields.subject')}
                                        </label>
                                        <input
                                            type="text"
                                            id="subject"
                                            name="subject"
                                            value={formData.subject}
                                            onChange={handleChange}
                                            placeholder={t('contact.form.placeholders.subject')}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600"
                                            required
                                        />
                                    </div>

                                    <div>
                                        <label htmlFor="message" className="block text-sm font-medium mb-2">
                                            {t('contact.form.fields.message')}
                                        </label>
                                        <textarea
                                            id="message"
                                            name="message"
                                            value={formData.message}
                                            onChange={handleChange}
                                            placeholder={t('contact.form.placeholders.message')}
                                            rows={5}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600"
                                            required
                                        />
                                    </div>

                                    <Button type="submit" className="w-full">
                                        <Send className="h-4 w-4 mr-2" />
                                        {t('contact.form.submitButton')}
                                    </Button>
                                </form>
                            </CardContent>
                        </Card>
                    </div>
                </div>

                {/* Support Categories */}
                <div className="mt-20">
                    <h2 className="text-3xl font-bold mb-8 text-center">{t('contact.supportCategories.title')}</h2>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                        {supportCategories.map((category, index) => (
                            <Card key={index}>
                                <CardContent className="p-6">
                                    <div className="flex items-center mb-4">
                                        <div className="bg-indigo-100 p-3 rounded-lg text-indigo-600 mr-4 dark:bg-indigo-900 dark:text-indigo-300">
                                            {category.icon}
                                        </div>
                                        <h3 className="text-xl font-bold">{category.title}</h3>
                                    </div>
                                    <p className="text-gray-700 dark:text-gray-300 mb-4">{category.description}</p>
                                    <a href={`mailto:${category.email}`} className="text-indigo-600 hover:text-indigo-800 font-medium">
                                        {category.email}
                                    </a>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                </div>

                {/* FAQ */}
                <div className="mt-20">
                    <h2 className="text-3xl font-bold mb-8 text-center">{t('contact.faq.title')}</h2>
                    <div className="max-w-3xl mx-auto space-y-6">
                        {faqs.map((faq, index) => (
                            <Card key={index}>
                                <CardContent className="p-6">
                                    <h3 className="text-lg font-semibold mb-2">{faq.question}</h3>
                                    <p className="text-gray-700 dark:text-gray-300">{faq.answer}</p>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                </div>
            </div>
        </PublicLayout>
    )
}
