'use client'

import Link from 'next/link'
import { useTranslations } from 'next-intl'
import { ChevronLeft, Check, Star } from 'lucide-react'
import PublicLayout from '@/components/layout/PublicLayout'
import { Card, CardContent } from '@/components/ui/Card'
import { But<PERSON> } from '@/components/ui/Button'

export default function PricingPage() {
    const t = useTranslations()

    return (
        <PublicLayout>
            <div className="container mx-auto py-12 px-4 sm:px-6 lg:px-8">
                <div className="mb-12">
                    <Link href="/" className="flex items-center text-indigo-600 hover:text-indigo-800 mb-4">
                        <ChevronLeft className="w-5 h-5 mr-1" />
                        <span>{t('common.backToHome')}</span>
                    </Link>
                    <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                        {t('pricing.title')}
                    </h1>
                    <p className="text-xl text-gray-600 dark:text-gray-300">
                        {t('pricing.subtitle')}
                    </p>
                </div>

                {/* Mô tả */}
                <div className="mb-16">
                    <Card>
                        <CardContent className="p-8">
                            <p className="text-lg leading-relaxed">
                                {t('pricing.description')}
                            </p>
                        </CardContent>
                    </Card>
                </div>

                {/* Coming Soon Section */}
                <div className="text-center mb-16">
                    <div className="bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-lg p-12">
                        <Star className="h-16 w-16 mx-auto mb-6 text-yellow-300" />
                        <h2 className="text-3xl font-bold mb-4">{t('pricing.comingSoon')}</h2>
                        <p className="text-xl mb-8 max-w-2xl mx-auto">
                            {t('pricing.comingSoonDescription')}
                        </p>
                        <div className="flex flex-col sm:flex-row gap-4 justify-center">
                            <Link href="/register">
                                <Button size="lg" variant="secondary" className="px-8">
                                    {t('home.cta.getStarted')}
                                </Button>
                            </Link>
                            <Link href="/contact">
                                <Button size="lg" variant="outline" className="px-8 border-white text-white hover:bg-white hover:text-indigo-600">
                                    {t('pricing.cta.contact')}
                                </Button>
                            </Link>
                        </div>
                    </div>
                </div>

                {/* CTA Section */}
                <div className="text-center">
                    <h3 className="text-2xl font-bold mb-4">{t('pricing.cta.title')}</h3>
                    <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-2xl mx-auto">
                        {t('pricing.cta.description')}
                    </p>
                    <div className="flex flex-col sm:flex-row justify-center gap-4">
                        <Link href="/register">
                            <Button size="lg" className="px-8">
                                {t('home.cta.getStarted')}
                            </Button>
                        </Link>
                        <Link href="/contact">
                            <Button size="lg" variant="outline" className="px-8">
                                {t('pricing.cta.contact')}
                            </Button>
                        </Link>
                    </div>
                </div>
            </div>
        </PublicLayout>
    )
}
