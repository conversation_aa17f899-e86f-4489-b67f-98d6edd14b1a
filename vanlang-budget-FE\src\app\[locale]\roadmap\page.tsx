'use client'

import Link from 'next/link'
import { useTranslations } from 'next-intl'
import { ChevronLeft, CheckCircle, Clock, Star, Zap } from 'lucide-react'
import PublicLayout from '@/components/layout/PublicLayout'
import { Card, CardContent } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'

export default function RoadmapPage() {
    const t = useTranslations()

    const roadmapItems = [
        {
            quarter: t('roadmap.items.q1.quarter'),
            title: t('roadmap.items.q1.title'),
            description: t('roadmap.items.q1.description'),
            features: t.raw('roadmap.items.q1.features') as string[],
            status: 'completed',
            icon: <CheckCircle className="h-8 w-8" />
        },
        {
            quarter: t('roadmap.items.q2.quarter'),
            title: t('roadmap.items.q2.title'),
            description: t('roadmap.items.q2.description'),
            features: t.raw('roadmap.items.q2.features') as string[],
            status: 'in-progress',
            icon: <Zap className="h-8 w-8" />
        },
        {
            quarter: t('roadmap.items.q3.quarter'),
            title: t('roadmap.items.q3.title'),
            description: t('roadmap.items.q3.description'),
            features: t.raw('roadmap.items.q3.features') as string[],
            status: 'planned',
            icon: <Star className="h-8 w-8" />
        },
        {
            quarter: t('roadmap.items.q4.quarter'),
            title: t('roadmap.items.q4.title'),
            description: t('roadmap.items.q4.description'),
            features: t.raw('roadmap.items.q4.features') as string[],
            status: 'planned',
            icon: <Clock className="h-8 w-8" />
        }
    ]

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'completed':
                return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
            case 'in-progress':
                return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
            case 'planned':
                return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
            default:
                return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
        }
    }

    const getStatusText = (status: string) => {
        switch (status) {
            case 'completed':
                return t('roadmap.status.completed')
            case 'in-progress':
                return t('roadmap.status.inProgress')
            case 'planned':
                return t('roadmap.status.planned')
            default:
                return t('roadmap.status.planned')
        }
    }

    return (
        <PublicLayout>
            <div className="container mx-auto py-12 px-4 sm:px-6 lg:px-8">
                <div className="mb-12">
                    <Link href="/" className="flex items-center text-indigo-600 hover:text-indigo-800 mb-4">
                        <ChevronLeft className="w-5 h-5 mr-1" />
                        <span>{t('common.backToHome')}</span>
                    </Link>
                    <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                        {t('roadmap.title')}
                    </h1>
                    <p className="text-xl text-gray-600 dark:text-gray-300">
                        {t('roadmap.subtitle')}
                    </p>
                </div>

                {/* Mô tả */}
                <div className="mb-16">
                    <Card>
                        <CardContent className="p-8">
                            <p className="text-lg leading-relaxed">
                                {t('roadmap.description')}
                            </p>
                        </CardContent>
                    </Card>
                </div>

                {/* Roadmap Timeline */}
                <div className="space-y-8">
                    {roadmapItems.map((item, index) => (
                        <div key={index} className="relative">
                            {/* Timeline line */}
                            {index < roadmapItems.length - 1 && (
                                <div className="absolute left-8 top-16 w-0.5 h-full bg-gray-300 dark:bg-gray-600 -z-10"></div>
                            )}
                            
                            <Card className="ml-20">
                                <CardContent className="p-8">
                                    <div className="flex items-start">
                                        {/* Icon */}
                                        <div className={`absolute left-4 p-3 rounded-full ${
                                            item.status === 'completed' 
                                                ? 'bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-300'
                                                : item.status === 'in-progress'
                                                ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300'
                                                : 'bg-gray-100 text-gray-600 dark:bg-gray-900 dark:text-gray-300'
                                        }`}>
                                            {item.icon}
                                        </div>

                                        <div className="flex-1">
                                            <div className="flex items-center justify-between mb-4">
                                                <div>
                                                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                                                        {item.title}
                                                    </h3>
                                                    <p className="text-lg text-indigo-600 dark:text-indigo-400 font-medium">
                                                        {item.quarter}
                                                    </p>
                                                </div>
                                                <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(item.status)}`}>
                                                    {getStatusText(item.status)}
                                                </span>
                                            </div>

                                            <p className="text-gray-700 dark:text-gray-300 mb-6 text-lg">
                                                {item.description}
                                            </p>

                                            <div>
                                                <h4 className="font-semibold text-gray-900 dark:text-white mb-3">
                                                    {t('roadmap.featuresLabel')}:
                                                </h4>
                                                <ul className="grid grid-cols-1 md:grid-cols-2 gap-2">
                                                    {item.features.map((feature, featureIndex) => (
                                                        <li key={featureIndex} className="flex items-start">
                                                            <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                                                            <span className="text-gray-700 dark:text-gray-300">{feature}</span>
                                                        </li>
                                                    ))}
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    ))}
                </div>

                {/* Vision Section */}
                <div className="mt-20">
                    <Card className="bg-gradient-to-r from-indigo-500 to-purple-600 text-white">
                        <CardContent className="p-12 text-center">
                            <Star className="h-16 w-16 mx-auto mb-6 text-yellow-300" />
                            <h2 className="text-3xl font-bold mb-4">{t('roadmap.vision.title')}</h2>
                            <p className="text-xl mb-8 max-w-3xl mx-auto">
                                {t('roadmap.vision.description')}
                            </p>
                            <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                <Link href="/register">
                                    <Button size="lg" variant="secondary" className="px-8">
                                        {t('home.cta.getStarted')}
                                    </Button>
                                </Link>
                                <Link href="/contact">
                                    <Button size="lg" variant="outline" className="px-8 border-white text-white hover:bg-white hover:text-indigo-600">
                                        {t('roadmap.vision.feedback')}
                                    </Button>
                                </Link>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* CTA */}
                <div className="mt-20 text-center">
                    <h3 className="text-2xl font-bold mb-4">{t('roadmap.cta.title')}</h3>
                    <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-2xl mx-auto">
                        {t('roadmap.cta.description')}
                    </p>
                    <div className="flex flex-col sm:flex-row justify-center gap-4">
                        <Link href="/register">
                            <Button size="lg" className="px-8">
                                {t('home.cta.getStarted')}
                            </Button>
                        </Link>
                        <Link href="/contact">
                            <Button size="lg" variant="outline" className="px-8">
                                {t('roadmap.cta.contact')}
                            </Button>
                        </Link>
                    </div>
                </div>
            </div>
        </PublicLayout>
    )
}
